import os
import glob
import pandas as pd
import re
import logging
import configparser

def load_config(config_file="config.ini"):
    """Loads configuration from a file."""
    config = configparser.ConfigParser()
    config.read(config_file)
    return config

def setup_logging():
    """Sets up basic logging configuration."""
    # Create logs directory if it doesn't exist
    logs_dir = "logs"
    os.makedirs(logs_dir, exist_ok=True)

    logging.basicConfig(
        filename=os.path.join(logs_dir, "cmbcsv.log"),
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
    )

def extract_segment(path, pattern):
    """Extracts a segment from the path using the given regex pattern."""
    match = re.search(pattern, path)
    if match:
        return match.group(1)
    else:
        logging.warning(f"Desired segment not found in path: {path}")
        return None

def main():
    """Combines CSV files, cleans data, and saves the result."""
    setup_logging()
    config = load_config()

    try:
        path = input("Enter path of files for combining: ")
        os.chdir(path)
    except FileNotFoundError:
        logging.error(f"Directory not found: {path}")
        return
    except OSError as e:
        logging.error(f"Error changing directory to {path}: {e}")
        return

    try:
        regex_pattern = config["settings"]["regex_pattern"]
        name_column = config["columns"]["name_column"]
        email_column = config["columns"]["email_column"]
    except (KeyError, ValueError) as e:
        logging.error(f"Error reading configuration: {e}")
        return

    csn = extract_segment(path, regex_pattern)
    if csn is None:
        csn = "combined_data"

    csv_files = glob.glob("*.csv")
    if not csv_files:
        logging.warning(f"No CSV files found in: {path}")
        return

    try:
        df = pd.concat(
            [pd.read_csv(f, on_bad_lines="skip", low_memory=False) for f in csv_files],
            ignore_index=True,
        )
    except pd.errors.EmptyDataError:
        logging.error(f"All CSV files are empty in: {path}")
        return

    if name_column in df.columns:
        df.rename(columns={name_column: "Name"}, inplace=True)
    if email_column in df.columns:
        df.rename(columns={email_column: "Email"}, inplace=True)

    if "Name" not in df.columns:
        logging.error(f"No 'Name' column found in the files.")
        return
    if "Email" not in df.columns:
        logging.error(f"No 'Email' column found in the files.")
        return

    df.dropna(subset="Name", inplace=True)
    df.drop_duplicates(subset="Email", inplace=True)

    try:
        os.makedirs("unified", exist_ok=True)
    except OSError as e:
        logging.error(f"Error creating 'unified' directory: {e}")
        return

    filename = csn
    try:
        df[["Name", "Email"]].to_csv(
            "unified/" + filename + ".csv", encoding="utf-8-sig", mode="w+", index=False
        )
        logging.info(f"Combined data saved to: unified/{filename}.csv")
    except Exception as e:
        logging.error(f"Error saving combined data to CSV: {e}")
        return

if __name__ == "__main__":
    main()
