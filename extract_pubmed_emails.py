#!/usr/bin/env python3
"""
PubMed Email Extractor

This script extracts author names and email addresses from PubMed data files.
It processes the structured format and creates a CSV output with Author Name and Email columns.

Author: AI Assistant
Date: 2025-09-17
"""

import os
import re
import pandas as pd
import warnings
from datetime import datetime

# Import rich_progress for gradient progress bars
try:
    import rich_progress
    HAS_RICH = True
except ImportError:
    HAS_RICH = False
    print("Note: rich_progress not available. Using basic progress indicators.")

from pandas.core.common import SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)

def print_status(message, status_type="info"):
    """Print status message with or without rich formatting."""
    if HAS_RICH:
        rich_progress.print_status(message, status_type)
    else:
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")

def extract_emails_from_pubmed(file_path):
    """
    Extract author names and email addresses from PubMed data file.
    
    Args:
        file_path (str): Path to the PubMed data file
        
    Returns:
        pandas.DataFrame: DataFrame with Author Name and Email columns
    """
    print_status("Starting PubMed email extraction", "header")
    print_status(f"Processing file: {file_path}", "info")
    
    # Check if file exists
    if not os.path.exists(file_path):
        print_status(f"Error: File not found: {file_path}", "error")
        return pd.DataFrame(columns=['Author Name', 'Email'])
    
    # Read the file
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
    except UnicodeDecodeError:
        try:
            with open(file_path, 'r', encoding='latin-1') as file:
                content = file.read()
        except Exception as e:
            print_status(f"Error reading file: {str(e)}", "error")
            return pd.DataFrame(columns=['Author Name', 'Email'])
    
    # Split content into lines
    lines = content.split('\n')
    
    # Initialize variables
    extracted_data = []
    current_author = None
    current_author_full = None
    
    # Email pattern
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    
    print_status(f"Processing {len(lines)} lines", "info")
    
    # Process each line
    in_affiliation = False
    current_affiliation = ""

    for i, line in enumerate(lines):
        line = line.strip()

        # Check for Full Author Name (FAU)
        if line.startswith('FAU - '):
            current_author_full = line[6:].strip()  # Remove 'FAU - ' prefix
            current_author = None  # Reset abbreviated name
            in_affiliation = False
            current_affiliation = ""

        # Check for Author abbreviated name (AU)
        elif line.startswith('AU  - '):
            current_author = line[6:].strip()  # Remove 'AU  - ' prefix
            in_affiliation = False
            current_affiliation = ""

        # Check for Author affiliation/address (AD) that might contain email
        elif line.startswith('AD  - '):
            in_affiliation = True
            current_affiliation = line[6:].strip()  # Remove 'AD  - ' prefix

        # Check for continuation lines (lines that start with spaces and are part of affiliation)
        elif in_affiliation and line and not line.startswith(('FAU', 'AU ', 'AD ', 'PMID', 'LA ', 'GR ', 'CI ', 'AUID', 'OWN', 'STAT', 'DCOM', 'LR ', 'IS ', 'VI ', 'IP ', 'DP ', 'TI ', 'PG ', 'LID', 'AB ', 'PT ', 'DEP')):
            # This is likely a continuation of the affiliation
            current_affiliation += " " + line

        # Process affiliation when we reach the end or start of a new field
        if in_affiliation and (line.startswith(('FAU', 'AU ', 'AD ', 'PMID', 'LA ', 'GR ', 'CI ', 'AUID')) or i == len(lines) - 1):
            # Search for email addresses in the complete affiliation
            emails = re.findall(email_pattern, current_affiliation)

            if emails and (current_author_full or current_author):
                for email in emails:
                    # Use full name if available, otherwise use abbreviated name
                    author_name = current_author_full if current_author_full else current_author

                    # Clean up author name (remove extra spaces, commas at the end)
                    if author_name:
                        author_name = author_name.strip().rstrip(',')

                        extracted_data.append({
                            'Author Name': author_name,
                            'Email': email.lower()  # Convert email to lowercase
                        })

                        print_status(f"Found: {author_name} - {email}", "success")

            # Reset affiliation if starting a new field (not AD)
            if not line.startswith('AD  - '):
                in_affiliation = False
                current_affiliation = ""

        # Reset author info when we encounter a new record (PMID)
        if line.startswith('PMID- '):
            current_author = None
            current_author_full = None
            in_affiliation = False
            current_affiliation = ""
    
    # Create DataFrame
    df = pd.DataFrame(extracted_data)
    
    if len(df) > 0:
        # Remove duplicates based on email address
        df_unique = df.drop_duplicates(subset=['Email'], keep='first')
        
        print_status(f"Total records found: {len(df)}", "info")
        print_status(f"Unique emails: {len(df_unique)}", "info")
        
        return df_unique
    else:
        print_status("No email addresses found in the file", "warning")
        return pd.DataFrame(columns=['Author Name', 'Email'])

def main():
    """Main function to run the email extraction process."""
    
    # Input file path
    input_file = "pmc/pubmed-cancer.txt"
    
    # Output file path
    output_file = "pubmed_cancer_emails.csv"
    
    # Extract emails and author names
    df_emails = extract_emails_from_pubmed(input_file)
    
    if len(df_emails) > 0:
        # Save to CSV
        df_emails.to_csv(output_file, index=False, encoding='utf-8-sig')
        print_status(f"Results saved to: {output_file}", "success")
        
        # Display summary
        print_status("Extraction Summary:", "header")
        print_status(f"Total unique email addresses extracted: {len(df_emails)}", "info")
        
        # Show first few records
        if len(df_emails) > 0:
            print_status("Sample records:", "info")
            for i, row in df_emails.head(10).iterrows():
                print_status(f"  {row['Author Name']} - {row['Email']}", "info")
                
            if len(df_emails) > 10:
                print_status(f"  ... and {len(df_emails) - 10} more records", "info")
    else:
        print_status("No data to save", "warning")

if __name__ == "__main__":
    main()
