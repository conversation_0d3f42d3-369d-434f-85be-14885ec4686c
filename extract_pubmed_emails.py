#!/usr/bin/env python3
"""
PubMed Email Extractor

This script extracts author names and email addresses from PubMed data files.
It processes the structured format and creates a CSV output with Author Name and Email columns.

Author: AI Assistant
Date: 2025-09-17
"""

import os
import re
import pandas as pd
import warnings
from datetime import datetime

# Import rich_progress for gradient progress bars
try:
    import rich_progress
    HAS_RICH = True
except ImportError:
    HAS_RICH = False
    print("Note: rich_progress not available. Using basic progress indicators.")

from pandas.core.common import SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)

def print_status(message, status_type="info"):
    """Print status message with or without rich formatting."""
    if HAS_RICH:
        rich_progress.print_status(message, status_type)
    else:
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")

def extract_emails_from_pubmed(file_path):
    """
    Extract author names and email addresses from PubMed data file.

    Args:
        file_path (str): Path to the PubMed data file

    Returns:
        pandas.DataFrame: DataFrame with Author Name and Email columns
    """
    print_status("Starting PubMed email extraction", "header")
    print_status(f"Processing file: {file_path}", "info")

    # Check if file exists
    if not os.path.exists(file_path):
        print_status(f"Error: File not found: {file_path}", "error")
        return pd.DataFrame(columns=['Author Name', 'Email'])

    # Read the file
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()
    except UnicodeDecodeError:
        try:
            with open(file_path, 'r', encoding='latin-1') as file:
                lines = file.readlines()
        except Exception as e:
            print_status(f"Error reading file: {str(e)}", "error")
            return pd.DataFrame(columns=['Author Name', 'Email'])

    # Initialize variables
    extracted_data = []
    current_author_full = None

    # Email pattern
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'

    print_status(f"Processing {len(lines)} lines", "info")

    # Process each line
    for i, line in enumerate(lines):
        line = line.strip()

        # Check for Full Author Name (FAU)
        if line.startswith('FAU - '):
            current_author_full = line[6:].strip()  # Remove 'FAU - ' prefix

        # Check for any line that contains an email address
        emails = re.findall(email_pattern, line)

        if emails and current_author_full:
            for email in emails:
                # Clean up author name (remove extra spaces, commas at the end)
                author_name = current_author_full.strip().rstrip(',')

                extracted_data.append({
                    'Author Name': author_name,
                    'Email': email.lower()  # Convert email to lowercase
                })

                print_status(f"Found: {author_name} - {email}", "success")

        # Reset author info when we encounter a new record (PMID)
        if line.startswith('PMID- '):
            current_author_full = None
    
    # Create DataFrame
    df = pd.DataFrame(extracted_data)
    
    if len(df) > 0:
        # Remove duplicates based on email address
        df_unique = df.drop_duplicates(subset=['Email'], keep='first')
        
        print_status(f"Total records found: {len(df)}", "info")
        print_status(f"Unique emails: {len(df_unique)}", "info")
        
        return df_unique
    else:
        print_status("No email addresses found in the file", "warning")
        return pd.DataFrame(columns=['Author Name', 'Email'])

def main():
    """Main function to run the email extraction process."""

    # Create output directory if it doesn't exist
    output_dir = "output"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print_status(f"Created output directory: {output_dir}", "info")

    # Input file path
    input_file = "pmc/pubmed-cancer.txt"

    # Output file path
    output_file = os.path.join(output_dir, "pubmed_cancer_emails.csv")
    
    # Extract emails and author names
    df_emails = extract_emails_from_pubmed(input_file)
    
    if len(df_emails) > 0:
        # Save to CSV
        df_emails.to_csv(output_file, index=False, encoding='utf-8-sig')
        print_status(f"Results saved to: {output_file}", "success")
        
        # Display summary
        print_status("Extraction Summary:", "header")
        print_status(f"Total unique email addresses extracted: {len(df_emails)}", "info")
        
        # Show first few records
        if len(df_emails) > 0:
            print_status("Sample records:", "info")
            for i, row in df_emails.head(10).iterrows():
                print_status(f"  {row['Author Name']} - {row['Email']}", "info")
                
            if len(df_emails) > 10:
                print_status(f"  ... and {len(df_emails) - 10} more records", "info")
    else:
        print_status("No data to save", "warning")

if __name__ == "__main__":
    main()
