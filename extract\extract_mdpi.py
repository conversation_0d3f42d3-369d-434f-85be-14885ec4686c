import os
import glob
import pandas as pd
import numpy as np
import re
import shutil
import warnings

# Ignore SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=pd.errors.SettingWithCopyWarning)

def clean_email(email):
    """Cleans up an email string by removing leading/trailing whitespace and extra characters."""
    if isinstance(email, str):
        return email.strip(',;() ')
    return email

def clean_name(name):
    """Cleans up a name string by reordering and removing unwanted words."""
    if isinstance(name, str):
        name = name.strip()
        parts = name.split(', ')
        if len(parts) == 2:
            name = f"{parts[1]} {parts[0]}"
        name = name.strip()
        if 'behalf' in name.lower() or 'group' in name.lower():
            return np.nan
        return name
    return name

def process_txt_files(directory):
    """
    Processes multiple TXT files in a directory, extracts author and email information,
    cleans the data, and saves the result to a single CSV file.

    Args:
        directory: The directory containing the TXT files.
    """
    try:
        os.chdir(directory)
    except FileNotFoundError:
        print(f"Error: Directory not found: {directory}")
        return

    txt_files = glob.glob("*.txt")
    if not txt_files:
        print(f"Error: No TXT files found in: {directory}")
        return

    try:
        df_concat = pd.concat(
            [pd.read_table(f, on_bad_lines="skip", low_memory=False) for f in txt_files],
            ignore_index=True,
        )
    except Exception as e:
        print(f"Error reading or concatenating TXT files: {e}")
        return
    
    if df_concat.empty:
        print(f"Error: The dataframe is empty.")
        return

    # Rename columns if they exist
    if "AUTHOR" in df_concat.columns:
        df_concat.rename(columns={"AUTHOR": "Name"}, inplace=True)
    if "EMAIL " in df_concat.columns:
        df_concat.rename(columns={"EMAIL ": "Email"}, inplace=True)

    # Check if 'Name' and 'Email' columns exist
    if 'Name' not in df_concat.columns or 'Email' not in df_concat.columns:
        print("Error: 'Name' or 'Email' column not found in the TXT files.")
        return

    # Split names and emails
    df_concat['Name'] = df_concat['Name'].str.split(';')
    df_concat['Email'] = df_concat['Email'].str.split(';')

    # Explode the lists into separate rows
    df_exploded = df_concat.explode(['Name', 'Email'])

    # Clean up names and emails
    df_exploded['Email'] = df_exploded['Email'].apply(clean_email)
    df_exploded['Name'] = df_exploded['Name'].apply(clean_name)

    # Drop rows with NaN in 'Email' or 'Name'
    df_cleaned = df_exploded.dropna(subset=['Email', 'Name'])

    # Filter emails
    df_cleaned = df_cleaned[df_cleaned['Email'].str.contains('@', na=False)]
    df_cleaned = df_cleaned[~df_cleaned['Email'].str.contains('info|contact', case=False, na=False)]

    # Remove emails containing apostrophe (') or its HTML entity (&#039)
    df_cleaned = df_cleaned[~df_cleaned['Email'].str.contains("'|&#039", na=False)]

    # Drop duplicates
    df_unique = df_cleaned.drop_duplicates(subset='Email', keep='first')

    # Create output directory if it doesn't exist
    output_dir = os.path.join(directory, "output")
    os.makedirs(output_dir, exist_ok=True)

    # Save to CSV
    output_filepath = os.path.join(output_dir, "output.csv")
    df_unique.to_csv(output_filepath, columns=['Name', 'Email'], encoding='utf-8-sig', index=False)

    print(f"Data saved to: {output_filepath}")
    print(f"Total rows in output file: {len(df_unique)}")

def main():
    """Main function to run the script."""
    directory = input("Enter the directory containing the TXT files: ")
    process_txt_files(directory)

if __name__ == "__main__":
    main()
